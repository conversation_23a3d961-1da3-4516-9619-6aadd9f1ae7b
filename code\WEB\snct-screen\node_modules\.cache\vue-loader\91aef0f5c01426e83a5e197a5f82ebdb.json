{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754033586846}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <!-- 直接使用 bigemap 原生方式加载地图 -->\r\n        <div id=\"mapDiv\" style=\"width: 98%;height: 97%;margin: 10px auto;\">\r\n        </div>\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { currentGET } from \"api/modules\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nimport { mapHost, mapAccessToken } from '@/utils/host';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      map: null, // bigemap 地图实例\r\n      code: \"china\", // 当前区域代码\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n      enterpriseMarkers: [], // 企业标记数组\r\n      regionData: [], // 区域数据\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    this.initMap();\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n    // 清理地图资源\r\n    this.clearEnterpriseMarkers();\r\n  },\r\n  methods: {\r\n    // 初始化 bigemap 地图\r\n    initMap() {\r\n      // 等待 bigemap 库加载完成\r\n      this.waitForBigemap().then(() => {\r\n        try {\r\n          // 设置地图配置\r\n          var gpsArr = [30.0, 120.0]; // 默认中心点 [纬度, 经度]\r\n          BM.Config.HTTP_URL = mapHost;\r\n          BM.accessToken = mapAccessToken;\r\n\r\n          // 创建地图实例\r\n          this.map = BM.map('mapDiv', 'bigemap.bvgwuwcf', {\r\n            center: [gpsArr[0], gpsArr[1]],\r\n            zoom: 5,\r\n            zoomControl: true,\r\n            minZoom: 1,\r\n            maxZoom: 18,\r\n            attributionControl: false\r\n          });\r\n\r\n          console.log('地图初始化完成');\r\n\r\n          // 地图加载完成后的回调\r\n          this.map.on('load', () => {\r\n            console.log('地图加载完成');\r\n            // 如果有区域数据，显示企业标记\r\n            if (this.regionData.length > 0) {\r\n              this.displayEnterpriseMarkers(this.regionData);\r\n            }\r\n          });\r\n\r\n        } catch (error) {\r\n          console.error('地图初始化失败:', error);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 等待 bigemap 库加载\r\n    waitForBigemap() {\r\n      return new Promise((resolve) => {\r\n        if (typeof BM !== 'undefined') {\r\n          resolve();\r\n        } else {\r\n          const checkBM = setInterval(() => {\r\n            if (typeof BM !== 'undefined') {\r\n              clearInterval(checkBM);\r\n              resolve();\r\n            }\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.code = code;\r\n          this.regionData = res.data.dataList || [];\r\n          this.displayEnterpriseMarkers(this.regionData);\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 显示企业标记\r\n    displayEnterpriseMarkers(dataList) {\r\n      if (!this.map || !dataList) return;\r\n\r\n      // 清除现有标记\r\n      this.clearEnterpriseMarkers();\r\n\r\n      // 添加新标记\r\n      dataList.forEach((item) => {\r\n        // 根据实际数据结构调整\r\n        // 假设数据包含 name, longitude, latitude, value 等字段\r\n        if (item.longitude && item.latitude) {\r\n          try {\r\n            // 创建标记图标\r\n            const icon = BM.icon({\r\n              iconUrl: this.getEnterpriseIconUrl(item.value),\r\n              iconSize: this.getEnterpriseIconSize(item.value),\r\n              iconAnchor: [16, 16]\r\n            });\r\n\r\n            // 创建标记\r\n            const marker = BM.marker([item.latitude, item.longitude], {\r\n              icon: icon,\r\n              title: item.name\r\n            });\r\n\r\n            // 添加到地图\r\n            marker.addTo(this.map);\r\n\r\n            // 添加点击事件\r\n            marker.on('click', () => {\r\n              this.showEnterprisePopup(item);\r\n            });\r\n\r\n            this.enterpriseMarkers.push(marker);\r\n          } catch (error) {\r\n            console.error('添加企业标记失败:', error);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取企业图标URL\r\n    getEnterpriseIconUrl(value) {\r\n      // 根据设备数量返回不同的图标\r\n      if (value > 100) return '/img/markers/enterprise-large.png';\r\n      else if (value > 50) return '/img/markers/enterprise-medium.png';\r\n      else return '/img/markers/enterprise-small.png';\r\n    },\r\n\r\n    // 获取企业图标大小\r\n    getEnterpriseIconSize(value) {\r\n      if (value > 100) return [32, 32];\r\n      else if (value > 50) return [28, 28];\r\n      else return [24, 24];\r\n    },\r\n\r\n    // 显示企业信息弹窗\r\n    showEnterprisePopup(item) {\r\n      BM.popup()\r\n        .setLatLng([item.latitude, item.longitude])\r\n        .setContent(`\r\n          <div style=\"padding: 10px;\">\r\n            <h4>${item.name}</h4>\r\n            <p>设备数量: ${item.value || 0}个</p>\r\n          </div>\r\n        `)\r\n        .openOn(this.map);\r\n    },\r\n\r\n    // 清除企业标记\r\n    clearEnterpriseMarkers() {\r\n      this.enterpriseMarkers.forEach(marker => {\r\n        if (this.map && marker) {\r\n          this.map.removeLayer(marker);\r\n        }\r\n      });\r\n      this.enterpriseMarkers = [];\r\n    },\r\n\r\n    // 简化的消息提示方法\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .big-map-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
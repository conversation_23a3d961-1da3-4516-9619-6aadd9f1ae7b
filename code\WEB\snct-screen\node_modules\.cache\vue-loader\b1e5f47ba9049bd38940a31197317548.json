{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\MapControls.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\MapControls.vue", "mtime": 1754028423421}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapControls.vue"], "names": [], "mappings": ";AAoHA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "MapControls.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\n  <div class=\"map-controls\">\n    <!-- 图层切换控件 -->\n    <div class=\"control-group layer-control\">\n      <div class=\"control-title\">图层</div>\n      <div class=\"control-buttons\">\n        <el-button\n          v-for=\"layer in availableLayers\"\n          :key=\"layer.key\"\n          :type=\"currentLayer === layer.key ? 'primary' : 'default'\"\n          size=\"mini\"\n          @click=\"switchLayer(layer.key)\"\n        >\n          {{ layer.name }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 缩放控件 -->\n    <div class=\"control-group zoom-control\">\n      <div class=\"control-title\">缩放</div>\n      <div class=\"control-buttons\">\n        <el-button size=\"mini\" icon=\"el-icon-plus\" @click=\"zoomIn\" />\n        <el-button size=\"mini\" icon=\"el-icon-minus\" @click=\"zoomOut\" />\n        <el-button size=\"mini\" @click=\"resetView\">重置</el-button>\n      </div>\n    </div>\n\n    <!-- 工具控件 -->\n    <div class=\"control-group tool-control\">\n      <div class=\"control-title\">工具</div>\n      <div class=\"control-buttons\">\n        <el-button\n          size=\"mini\"\n          :type=\"fullscreen ? 'primary' : 'default'\"\n          icon=\"el-icon-full-screen\"\n          @click=\"toggleFullscreen\"\n        >\n          {{ fullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n        \n        <el-button\n          size=\"mini\"\n          :type=\"showNineLine ? 'primary' : 'default'\"\n          @click=\"toggleNineLine\"\n        >\n          九段线\n        </el-button>\n\n        <el-button\n          size=\"mini\"\n          :type=\"measureMode ? 'primary' : 'default'\"\n          @click=\"toggleMeasure\"\n        >\n          测距\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 显示控件 -->\n    <div class=\"control-group display-control\">\n      <div class=\"control-title\">显示</div>\n      <div class=\"control-buttons\">\n        <el-button\n          size=\"mini\"\n          :type=\"showCoordinates ? 'primary' : 'default'\"\n          @click=\"toggleCoordinates\"\n        >\n          坐标\n        </el-button>\n        \n        <el-button\n          size=\"mini\"\n          :type=\"showScale ? 'primary' : 'default'\"\n          @click=\"toggleScale\"\n        >\n          比例尺\n        </el-button>\n\n        <el-button\n          size=\"mini\"\n          :type=\"showOverview ? 'primary' : 'default'\"\n          @click=\"toggleOverview\"\n        >\n          鹰眼\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 坐标显示 -->\n    <div v-if=\"showCoordinates\" class=\"coordinate-display\">\n      <div class=\"coordinate-item\">\n        <span>经度: {{ currentCoordinates.lng }}</span>\n      </div>\n      <div class=\"coordinate-item\">\n        <span>纬度: {{ currentCoordinates.lat }}</span>\n      </div>\n      <div class=\"coordinate-item\">\n        <span>缩放: {{ currentZoom }}</span>\n      </div>\n    </div>\n\n    <!-- 测距结果显示 -->\n    <div v-if=\"measureMode && measureResult\" class=\"measure-result\">\n      <div class=\"measure-item\">\n        <span>距离: {{ measureResult.distance.toFixed(2) }} 海里</span>\n      </div>\n      <div class=\"measure-item\">\n        <span>方位: {{ measureResult.bearing.toFixed(1) }}°</span>\n      </div>\n      <el-button size=\"mini\" @click=\"clearMeasure\">清除</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapConfig } from '@/utils/map/mapConfig'\nimport { coordinateUtils } from '@/utils/map/coordinateUtils'\nimport { createNineLineLayer, removeNineLineLayer } from '@/utils/map/nineLineData'\n\nexport default {\n  name: 'MapControls',\n  props: {\n    // 地图实例\n    map: {\n      type: Object,\n      required: true\n    },\n    // 当前图层\n    currentLayer: {\n      type: String,\n      default: 'satellite'\n    }\n  },\n  data() {\n    return {\n      fullscreen: false,\n      showNineLine: false,\n      showCoordinates: true,\n      showScale: true,\n      showOverview: false,\n      measureMode: false,\n      measureResult: null,\n      measurePoints: [],\n      measureOverlays: [],\n      nineLineOverlays: [],\n      currentCoordinates: {\n        lng: '0.000000°',\n        lat: '0.000000°'\n      },\n      currentZoom: 0,\n      availableLayers: [\n        { key: 'satellite', name: '卫星图' },\n        { key: 'sea', name: '海图' },\n        { key: 'street', name: '街道图' }\n      ]\n    }\n  },\n  watch: {\n    map: {\n      handler(newMap) {\n        if (newMap) {\n          this.bindMapEvents()\n          this.updateMapInfo()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    /**\n     * 绑定地图事件\n     */\n    bindMapEvents() {\n      if (!this.map) return\n\n      // 鼠标移动事件\n      this.map.on('mousemove', (e) => {\n        if (this.showCoordinates) {\n          this.updateCoordinates(e.lngLat.lng, e.lngLat.lat)\n        }\n      })\n\n      // 缩放事件\n      this.map.on('zoomend', () => {\n        this.updateMapInfo()\n      })\n\n      // 移动事件\n      this.map.on('moveend', () => {\n        this.updateMapInfo()\n      })\n\n      // 点击事件（测距模式）\n      this.map.on('click', (e) => {\n        if (this.measureMode) {\n          this.addMeasurePoint(e.lngLat.lng, e.lngLat.lat)\n        }\n      })\n    },\n\n    /**\n     * 更新地图信息\n     */\n    updateMapInfo() {\n      if (!this.map) return\n\n      this.currentZoom = this.map.getZoom()\n      \n      const center = this.map.getCenter()\n      this.updateCoordinates(center.lng, center.lat)\n    },\n\n    /**\n     * 更新坐标显示\n     */\n    updateCoordinates(lng, lat) {\n      const formatted = coordinateUtils.formatCoordinate(lng, lat)\n      this.currentCoordinates = {\n        lng: formatted.lng,\n        lat: formatted.lat\n      }\n    },\n\n    /**\n     * 切换图层\n     */\n    switchLayer(layerKey) {\n      this.$emit('layer-change', layerKey)\n    },\n\n    /**\n     * 放大\n     */\n    zoomIn() {\n      if (this.map) {\n        const currentZoom = this.map.getZoom()\n        this.map.setZoom(currentZoom + 1)\n      }\n    },\n\n    /**\n     * 缩小\n     */\n    zoomOut() {\n      if (this.map) {\n        const currentZoom = this.map.getZoom()\n        this.map.setZoom(currentZoom - 1)\n      }\n    },\n\n    /**\n     * 重置视图\n     */\n    resetView() {\n      if (this.map) {\n        const center = new BM.LngLat(mapConfig.defaultCenter[0], mapConfig.defaultCenter[1])\n        this.map.setView(center, mapConfig.defaultZoom)\n      }\n    },\n\n    /**\n     * 切换全屏\n     */\n    toggleFullscreen() {\n      this.fullscreen = !this.fullscreen\n      this.$emit('fullscreen-change', this.fullscreen)\n    },\n\n    /**\n     * 切换九段线显示\n     */\n    toggleNineLine() {\n      this.showNineLine = !this.showNineLine\n      \n      if (this.showNineLine) {\n        this.nineLineOverlays = createNineLineLayer(this.map, mapConfig.nineLine)\n      } else {\n        removeNineLineLayer(this.map, this.nineLineOverlays)\n        this.nineLineOverlays = []\n      }\n    },\n\n    /**\n     * 切换坐标显示\n     */\n    toggleCoordinates() {\n      this.showCoordinates = !this.showCoordinates\n    },\n\n    /**\n     * 切换比例尺显示\n     */\n    toggleScale() {\n      this.showScale = !this.showScale\n      this.$emit('scale-change', this.showScale)\n    },\n\n    /**\n     * 切换鹰眼显示\n     */\n    toggleOverview() {\n      this.showOverview = !this.showOverview\n      this.$emit('overview-change', this.showOverview)\n    },\n\n    /**\n     * 切换测距模式\n     */\n    toggleMeasure() {\n      this.measureMode = !this.measureMode\n      \n      if (!this.measureMode) {\n        this.clearMeasure()\n      } else {\n        this.$message.info('点击地图上的两个点进行测距')\n      }\n    },\n\n    /**\n     * 添加测距点\n     */\n    addMeasurePoint(lng, lat) {\n      if (!this.measureMode) return\n\n      this.measurePoints.push({ lng, lat })\n\n      // 添加标记\n      const marker = new BM.Marker(new BM.LngLat(lng, lat), {\n        icon: new BM.Icon({\n          url: '/img/markers/measure-point.png',\n          size: new BM.Size(16, 16),\n          anchor: new BM.Size(8, 8)\n        })\n      })\n      \n      this.map.addOverlay(marker)\n      this.measureOverlays.push(marker)\n\n      // 如果有两个点，计算距离和方位\n      if (this.measurePoints.length === 2) {\n        this.calculateMeasureResult()\n        this.drawMeasureLine()\n      } else if (this.measurePoints.length > 2) {\n        // 重新开始测距\n        this.clearMeasure()\n        this.addMeasurePoint(lng, lat)\n      }\n    },\n\n    /**\n     * 计算测距结果\n     */\n    calculateMeasureResult() {\n      if (this.measurePoints.length < 2) return\n\n      const p1 = this.measurePoints[0]\n      const p2 = this.measurePoints[1]\n\n      const distance = coordinateUtils.calculateDistance(p1.lat, p1.lng, p2.lat, p2.lng)\n      const bearing = coordinateUtils.calculateBearing(p1.lat, p1.lng, p2.lat, p2.lng)\n\n      this.measureResult = {\n        distance,\n        bearing,\n        points: [p1, p2]\n      }\n    },\n\n    /**\n     * 绘制测距线\n     */\n    drawMeasureLine() {\n      if (this.measurePoints.length < 2) return\n\n      const points = this.measurePoints.map(p => new BM.LngLat(p.lng, p.lat))\n      \n      const polyline = new BM.Polyline(points, {\n        strokeColor: mapConfig.measurement.distance.strokeColor,\n        strokeWeight: mapConfig.measurement.distance.strokeWeight,\n        strokeOpacity: mapConfig.measurement.distance.strokeOpacity,\n        strokeStyle: 'dashed'\n      })\n\n      this.map.addOverlay(polyline)\n      this.measureOverlays.push(polyline)\n    },\n\n    /**\n     * 清除测距\n     */\n    clearMeasure() {\n      // 清除覆盖物\n      this.measureOverlays.forEach(overlay => {\n        this.map.removeOverlay(overlay)\n      })\n      \n      // 重置数据\n      this.measurePoints = []\n      this.measureOverlays = []\n      this.measureResult = null\n    }\n  },\n\n  beforeDestroy() {\n    // 清理资源\n    this.clearMeasure()\n    removeNineLineLayer(this.map, this.nineLineOverlays)\n  }\n}\n</script>\n\n<style scoped>\n.map-controls {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\n  padding: 15px;\n  min-width: 200px;\n  max-width: 300px;\n}\n\n.control-group {\n  margin-bottom: 15px;\n}\n\n.control-group:last-child {\n  margin-bottom: 0;\n}\n\n.control-title {\n  font-size: 12px;\n  font-weight: bold;\n  color: #666;\n  margin-bottom: 8px;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 4px;\n}\n\n.control-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n}\n\n.coordinate-display {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  margin-top: 10px;\n}\n\n.coordinate-item {\n  margin-bottom: 2px;\n}\n\n.coordinate-item:last-child {\n  margin-bottom: 0;\n}\n\n.measure-result {\n  background: rgba(255, 193, 7, 0.1);\n  border: 1px solid #ffc107;\n  padding: 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  margin-top: 10px;\n}\n\n.measure-item {\n  margin-bottom: 4px;\n}\n\n.measure-item:last-child {\n  margin-bottom: 8px;\n}\n</style>\n"]}]}
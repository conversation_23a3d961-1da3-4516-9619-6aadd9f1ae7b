<!--
 * @Author: daidai
 * @Date: 2022-03-01 11:17:39
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-29 15:50:18
 * @FilePath: \web-pc\src\pages\big-screen\view\indexs\center-map.vue
-->
<template>
  <div class="centermap">
    <div class="maptitle">
      <div class="zuo"></div>
      <span class="titletext">{{ maptitle }}</span>
      <div class="you"></div>
    </div>
    <div class="mapwrap">
      <dv-border-box-13>
        <!-- 直接使用 bigemap 原生方式加载地图 -->
        <div id="mapDiv" style="width: 98%;height: 97%;margin: 10px auto;">
        </div>
      </dv-border-box-13>
    </div>
  </div>
</template>

<script>
import { currentGET } from "api/modules";
import { dataModule } from '@/utils/webSocket';
import { mapHost, mapAccessToken } from '@/utils/host';

export default {
  data() {
    return {
      maptitle: "企业数量： 家 | 接入船总数量： 艘",
      map: null, // bigemap 地图实例
      code: "china", // 当前区域代码
      wsCheckTimer: null, // WebSocket数据监听定时器
      shipTotalCount: 0, // 船舶总数量
      enterpriseTotalCount: 0, // 企业总数量
      enterpriseMarkers: [], // 企业标记数组
      regionData: [], // 区域数据
    };
  },
  created() {},

  mounted() {
    this.initMap();
    this.getData("china");
    this.startDataMonitoring();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.wsCheckTimer) {
      clearInterval(this.wsCheckTimer);
      this.wsCheckTimer = null;
    }
    // 清理地图资源
    this.clearEnterpriseMarkers();
  },
  methods: {
    // 初始化 bigemap 地图
    initMap() {
      // 等待 bigemap 库加载完成
      this.waitForBigemap().then(() => {
        try {
          // 设置地图配置
          var gpsArr = [30.0, 120.0]; // 默认中心点 [纬度, 经度]
          BM.Config.HTTP_URL = mapHost;
          BM.accessToken = mapAccessToken;

          // 创建地图实例
          this.map = BM.map('mapDiv', 'bigemap.bvgwuwcf', {
            center: [gpsArr[0], gpsArr[1]],
            zoom: 5,
            zoomControl: true,
            minZoom: 1,
            maxZoom: 18,
            attributionControl: false
          });

          console.log('地图初始化完成');

          // 地图加载完成后的回调
          this.map.on('load', () => {
            console.log('地图加载完成');
            // 如果有区域数据，显示企业标记
            if (this.regionData.length > 0) {
              this.displayEnterpriseMarkers(this.regionData);
            }
          });

        } catch (error) {
          console.error('地图初始化失败:', error);
        }
      });
    },

    // 等待 bigemap 库加载
    waitForBigemap() {
      return new Promise((resolve) => {
        if (typeof BM !== 'undefined') {
          resolve();
        } else {
          const checkBM = setInterval(() => {
            if (typeof BM !== 'undefined') {
              clearInterval(checkBM);
              resolve();
            }
          }, 100);
        }
      });
    },

    // 开始WebSocket数据监听
    startDataMonitoring() {
      this.wsCheckTimer = setInterval(() => {
        this.checkWebSocketData();
      }, 1000);
    },

    // 检查WebSocket数据
    checkWebSocketData() {
      if (dataModule.D0A02) {
        const newData = dataModule.D0A02;
        // 检查数据是否有变化
        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {
          this.shipTotalCount = newData.ship_num || 0;
          this.enterpriseTotalCount = newData.enterprise_num || 0;

          // 更新地图标题显示
          this.updateMapTitle();

          console.log('船舶数量数据更新:', {
            ship_num: this.shipTotalCount,
            enterprise_num: this.enterpriseTotalCount
          });
        }
      }
    },

    // 更新地图标题
    updateMapTitle() {
      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;
    },

    getData(code) {
      currentGET("big8", { regionCode: code }).then((res) => {
        console.log("设备分布", res);
        if (res.success) {
          this.code = code;
          this.regionData = res.data.dataList || [];
          this.displayEnterpriseMarkers(this.regionData);
        } else {
          this.$Message.warning(res.msg);
        }
      });
    },

    // 显示企业标记
    displayEnterpriseMarkers(dataList) {
      if (!this.map || !dataList) return;

      // 清除现有标记
      this.clearEnterpriseMarkers();

      // 添加新标记
      dataList.forEach((item) => {
        // 根据实际数据结构调整
        // 假设数据包含 name, longitude, latitude, value 等字段
        if (item.longitude && item.latitude) {
          try {
            // 创建标记图标
            const icon = BM.icon({
              iconUrl: this.getEnterpriseIconUrl(item.value),
              iconSize: this.getEnterpriseIconSize(item.value),
              iconAnchor: [16, 16]
            });

            // 创建标记
            const marker = BM.marker([item.latitude, item.longitude], {
              icon: icon,
              title: item.name
            });

            // 添加到地图
            marker.addTo(this.map);

            // 添加点击事件
            marker.on('click', () => {
              this.showEnterprisePopup(item);
            });

            this.enterpriseMarkers.push(marker);
          } catch (error) {
            console.error('添加企业标记失败:', error);
          }
        }
      });
    },

    // 获取企业图标URL
    getEnterpriseIconUrl(value) {
      // 根据设备数量返回不同的图标
      if (value > 100) return '/img/markers/enterprise-large.png';
      else if (value > 50) return '/img/markers/enterprise-medium.png';
      else return '/img/markers/enterprise-small.png';
    },

    // 获取企业图标大小
    getEnterpriseIconSize(value) {
      if (value > 100) return [32, 32];
      else if (value > 50) return [28, 28];
      else return [24, 24];
    },

    // 显示企业信息弹窗
    showEnterprisePopup(item) {
      BM.popup()
        .setLatLng([item.latitude, item.longitude])
        .setContent(`
          <div style="padding: 10px;">
            <h4>${item.name}</h4>
            <p>设备数量: ${item.value || 0}个</p>
          </div>
        `)
        .openOn(this.map);
    },

    // 清除企业标记
    clearEnterpriseMarkers() {
      this.enterpriseMarkers.forEach(marker => {
        if (this.map && marker) {
          this.map.removeLayer(marker);
        }
      });
      this.enterpriseMarkers = [];
    },

    // 简化的消息提示方法
    message(text) {
      this.$Message({
        text: text,
        type: "warning",
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.centermap {
  .maptitle {
    display: flex;
    justify-content: right;
    margin: 25px 0 5px 0;
    box-sizing: border-box;

    .titletext {
      font-size: 26px;
      font-weight: 300;
      letter-spacing: 1px;
      background: linear-gradient(
        92deg,
        #0072ff 0%,
        #00eaff 48.8525390625%,
        #01aaff 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 16px;
    }

    .zuo,
    .you {
      background-size: 100% 100%;
      width: 26px;
      height: 16px;
      margin-top: 7px;
    }

    .zuo {
      background: url("../../assets/img/xiezuo.png") no-repeat;
    }

    .you {
      background: url("../../assets/img/xieyou.png") no-repeat;
    }
  }

  .mapwrap {
    height: 900px;
    width: 100%;
    box-sizing: border-box;
    position: relative;

    .big-map-container {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }
}
</style>

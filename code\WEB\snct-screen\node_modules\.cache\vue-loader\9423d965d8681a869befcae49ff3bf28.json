{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=style&index=0&id=10948d28&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754033586846}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2VudGVybWFwIHsNCiAgLm1hcHRpdGxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogcmlnaHQ7DQogICAgbWFyZ2luOiAyNXB4IDAgNXB4IDA7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCg0KICAgIC50aXRsZXRleHQgew0KICAgICAgZm9udC1zaXplOiAyNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDMwMDsNCiAgICAgIGxldHRlci1zcGFjaW5nOiAxcHg7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoDQogICAgICAgIDkyZGVnLA0KICAgICAgICAjMDA3MmZmIDAlLA0KICAgICAgICAjMDBlYWZmIDQ4Ljg1MjUzOTA2MjUlLA0KICAgICAgICAjMDFhYWZmIDEwMCUNCiAgICAgICk7DQogICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDsNCiAgICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDsNCiAgICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsNCiAgICAgIG1hcmdpbjogMCAxNnB4Ow0KICAgIH0NCg0KICAgIC56dW8sDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgIHdpZHRoOiAyNnB4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgbWFyZ2luLXRvcDogN3B4Ow0KICAgIH0NCg0KICAgIC56dW8gew0KICAgICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi9hc3NldHMvaW1nL3hpZXp1by5wbmciKSBuby1yZXBlYXQ7DQogICAgfQ0KDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uL2Fzc2V0cy9pbWcveGlleW91LnBuZyIpIG5vLXJlcGVhdDsNCiAgICB9DQogIH0NCg0KICAubWFwd3JhcCB7DQogICAgaGVpZ2h0OiA5MDBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgIC5iaWctbWFwLWNvbnRhaW5lciB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAwPA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <!-- 直接使用 bigemap 原生方式加载地图 -->\r\n        <div id=\"mapDiv\" style=\"width: 98%;height: 97%;margin: 10px auto;\">\r\n        </div>\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { currentGET } from \"api/modules\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nimport { mapHost, mapAccessToken } from '@/utils/host';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      map: null, // bigemap 地图实例\r\n      code: \"china\", // 当前区域代码\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n      enterpriseMarkers: [], // 企业标记数组\r\n      regionData: [], // 区域数据\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    this.initMap();\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n    // 清理地图资源\r\n    this.clearEnterpriseMarkers();\r\n  },\r\n  methods: {\r\n    // 初始化 bigemap 地图\r\n    initMap() {\r\n      // 等待 bigemap 库加载完成\r\n      this.waitForBigemap().then(() => {\r\n        try {\r\n          // 设置地图配置\r\n          var gpsArr = [30.0, 120.0]; // 默认中心点 [纬度, 经度]\r\n          BM.Config.HTTP_URL = mapHost;\r\n          BM.accessToken = mapAccessToken;\r\n\r\n          // 创建地图实例\r\n          this.map = BM.map('mapDiv', 'bigemap.bvgwuwcf', {\r\n            center: [gpsArr[0], gpsArr[1]],\r\n            zoom: 5,\r\n            zoomControl: true,\r\n            minZoom: 1,\r\n            maxZoom: 18,\r\n            attributionControl: false\r\n          });\r\n\r\n          console.log('地图初始化完成');\r\n\r\n          // 地图加载完成后的回调\r\n          this.map.on('load', () => {\r\n            console.log('地图加载完成');\r\n            // 如果有区域数据，显示企业标记\r\n            if (this.regionData.length > 0) {\r\n              this.displayEnterpriseMarkers(this.regionData);\r\n            }\r\n          });\r\n\r\n        } catch (error) {\r\n          console.error('地图初始化失败:', error);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 等待 bigemap 库加载\r\n    waitForBigemap() {\r\n      return new Promise((resolve) => {\r\n        if (typeof BM !== 'undefined') {\r\n          resolve();\r\n        } else {\r\n          const checkBM = setInterval(() => {\r\n            if (typeof BM !== 'undefined') {\r\n              clearInterval(checkBM);\r\n              resolve();\r\n            }\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.code = code;\r\n          this.regionData = res.data.dataList || [];\r\n          this.displayEnterpriseMarkers(this.regionData);\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 显示企业标记\r\n    displayEnterpriseMarkers(dataList) {\r\n      if (!this.map || !dataList) return;\r\n\r\n      // 清除现有标记\r\n      this.clearEnterpriseMarkers();\r\n\r\n      // 添加新标记\r\n      dataList.forEach((item) => {\r\n        // 根据实际数据结构调整\r\n        // 假设数据包含 name, longitude, latitude, value 等字段\r\n        if (item.longitude && item.latitude) {\r\n          try {\r\n            // 创建标记图标\r\n            const icon = BM.icon({\r\n              iconUrl: this.getEnterpriseIconUrl(item.value),\r\n              iconSize: this.getEnterpriseIconSize(item.value),\r\n              iconAnchor: [16, 16]\r\n            });\r\n\r\n            // 创建标记\r\n            const marker = BM.marker([item.latitude, item.longitude], {\r\n              icon: icon,\r\n              title: item.name\r\n            });\r\n\r\n            // 添加到地图\r\n            marker.addTo(this.map);\r\n\r\n            // 添加点击事件\r\n            marker.on('click', () => {\r\n              this.showEnterprisePopup(item);\r\n            });\r\n\r\n            this.enterpriseMarkers.push(marker);\r\n          } catch (error) {\r\n            console.error('添加企业标记失败:', error);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取企业图标URL\r\n    getEnterpriseIconUrl(value) {\r\n      // 根据设备数量返回不同的图标\r\n      if (value > 100) return '/img/markers/enterprise-large.png';\r\n      else if (value > 50) return '/img/markers/enterprise-medium.png';\r\n      else return '/img/markers/enterprise-small.png';\r\n    },\r\n\r\n    // 获取企业图标大小\r\n    getEnterpriseIconSize(value) {\r\n      if (value > 100) return [32, 32];\r\n      else if (value > 50) return [28, 28];\r\n      else return [24, 24];\r\n    },\r\n\r\n    // 显示企业信息弹窗\r\n    showEnterprisePopup(item) {\r\n      BM.popup()\r\n        .setLatLng([item.latitude, item.longitude])\r\n        .setContent(`\r\n          <div style=\"padding: 10px;\">\r\n            <h4>${item.name}</h4>\r\n            <p>设备数量: ${item.value || 0}个</p>\r\n          </div>\r\n        `)\r\n        .openOn(this.map);\r\n    },\r\n\r\n    // 清除企业标记\r\n    clearEnterpriseMarkers() {\r\n      this.enterpriseMarkers.forEach(marker => {\r\n        if (this.map && marker) {\r\n          this.map.removeLayer(marker);\r\n        }\r\n      });\r\n      this.enterpriseMarkers = [];\r\n    },\r\n\r\n    // 简化的消息提示方法\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .big-map-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
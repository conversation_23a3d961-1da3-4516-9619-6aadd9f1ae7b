{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\center-map.vue?vue&type=style&index=0&id=0e6ac80b&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\center-map.vue", "mtime": 1753934399259}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAuHA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA", "file": "center-map.vue", "sourceRoot": "src/views/comindexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div  style=\"margin-top: 15px;width: 56%;margin-top: 16px;\">\r\n      <button class=\"tech-button\" :class=\"{ 'active': isActive1 }\" @click=\"toggleActive1\">\r\n        <span class=\"text\">首页</span>\r\n      </button>\r\n      <button class=\"tech-button\" :class=\"{ 'active': isActive2 }\" @click=\"toggleActive2\">\r\n        <span class=\"text\">实时监控</span>\r\n      </button>\r\n      <button class=\"tech-button\" :class=\"{ 'active': isActive3 }\" @click=\"toggleActive3\">\r\n        <span class=\"text\">实时轨迹</span>\r\n      </button>\r\n      <button class=\"tech-button\" :class=\"{ 'active': isActive4 }\" @click=\"toggleActive4\">\r\n        <span class=\"text\">平台信息</span>\r\n      </button>\r\n    </div>\r\n    <div style=\"width: 16%;float: right;margin-right: 16px;margin-top: -8px;\" class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n        <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\" >\r\n      <dv-border-box-13 >\r\n          <div id=\"mapDiv\" style=\"width: 98%;height: 97%;background-color: aliceblue;margin: 10px auto;\">\r\n          </div>\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport xzqCode from \"../../utils/map/xzqCode\";\r\nimport { currentGET } from \"api/modules\";\r\nimport * as echarts from \"echarts\";\r\nimport { GETNOBASE } from \"api\";\r\nimport { mapHost,mapAccessToken,zoomLevel } from '@/utils/host';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      BM : null,\r\n      maptitle: \"接入船总数量：\",\r\n      options: {},\r\n      code: \"china\", //china 代表中国 其他地市是行政编码\r\n      echartBindClick: false,\r\n      isSouthChinaSea: false, //是否要展示南海群岛  修改此值请刷新页面\r\n      isActive1: true,\r\n      isActive2: false,\r\n      isActive3: false,\r\n      isActive4: false\r\n    };\r\n  },\r\n  created() {\r\n\r\n  },\r\n\r\n  mounted() {\r\n    // console.log(xzqCode);\r\n    //this.getData(\"china\");\r\n\r\n  var gpsArr = [24.84957504272461,118.05908203125];\r\n  BM.Config.HTTP_URL = mapHost;\r\n  BM.accessToken = mapAccessToken;\r\n\tvar map = BM.map('mapDiv', 'bigemap.bvgwuwcf', {\r\n\t\tcenter: [gpsArr[0], gpsArr[1]],\r\n\t\tzoom: 7,\r\n\t\tzoomControl: true,\r\n\t\tminZoom: 1,\r\n\t\tmaxZoom: 8,\r\n\t\tattributionControl: false\r\n\t});\r\n\r\n\r\n  },\r\n  methods: {\r\n    getData(code) {\r\n\r\n    },\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    },\r\n    toggleActive1() {\r\n      this.isActive1 = true\r\n      this.isActive2 = false\r\n      this.isActive3 = false\r\n      this.isActive4 = false\r\n    },\r\n    toggleActive2() {\r\n      this.isActive1 = false\r\n      this.isActive2 = true\r\n      this.isActive3 = false\r\n      this.isActive4 = false\r\n    },\r\n    toggleActive3() {\r\n      this.isActive1 = false\r\n      this.isActive2 = false\r\n      this.isActive3 = true\r\n      this.isActive4 = false\r\n    },\r\n    toggleActive4() {\r\n      this.isActive1 = false\r\n      this.isActive2 = false\r\n      this.isActive3 = false\r\n      this.isActive4 = true\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    height: 31px;\r\n    display: flex;\r\n    //justify-content: right;\r\n    box-sizing: border-box;\r\n    //margin: 35px 10px 0 0;\r\n\r\n    .titletext {\r\n      font-size: 19px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 14px;\r\n      margin-top: 2px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    margin-top: 8px;\r\n    height: 892px;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .quanguo {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: -46px;\r\n      width: 80px;\r\n      height: 28px;\r\n      border: 1px solid #00eded;\r\n      border-radius: 10px;\r\n      color: #00f7f6;\r\n      text-align: center;\r\n      line-height: 26px;\r\n      letter-spacing: 6px;\r\n      cursor: pointer;\r\n      box-shadow: 0 2px 4px rgba(0, 237, 237, 0.5),\r\n        0 0 6px rgba(0, 237, 237, 0.4);\r\n    }\r\n  }\r\n\r\n.tech-button {\r\n  position: relative;\r\n  padding: 12px 24px;\r\n  border: none;\r\n  background: linear-gradient(45deg, #101011, #15323f);\r\n  color: white;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(0,0,0,0.2);\r\n  overflow: hidden;\r\n}\r\n\r\n.tech-button.active {\r\n  background: linear-gradient(45deg, #1a2e3d, #b9cedf);\r\n  box-shadow: 0 8px 30px rgba(0,0,0,0.4);\r\n}\r\n\r\n}\r\n\r\n</style>\r\n"]}]}
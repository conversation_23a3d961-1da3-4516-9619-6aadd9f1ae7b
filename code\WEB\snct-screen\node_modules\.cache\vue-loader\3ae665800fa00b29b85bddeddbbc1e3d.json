{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\MapControls.vue?vue&type=style&index=0&id=0c682e7f&scoped=true&lang=css&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\MapControls.vue", "mtime": 1754028423421}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5tYXAtY29udHJvbHMgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDEwcHg7CiAgcmlnaHQ6IDEwcHg7CiAgei1pbmRleDogMTAwMDsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgcGFkZGluZzogMTVweDsKICBtaW4td2lkdGg6IDIwMHB4OwogIG1heC13aWR0aDogMzAwcHg7Cn0KCi5jb250cm9sLWdyb3VwIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouY29udHJvbC1ncm91cDpsYXN0LWNoaWxkIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9CgouY29udHJvbC10aXRsZSB7CiAgZm9udC1zaXplOiAxMnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjNjY2OwogIG1hcmdpbi1ib3R0b206IDhweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsKICBwYWRkaW5nLWJvdHRvbTogNHB4Owp9CgouY29udHJvbC1idXR0b25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtd3JhcDogd3JhcDsKICBnYXA6IDZweDsKfQoKLmNvb3JkaW5hdGUtZGlzcGxheSB7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpOwogIGNvbG9yOiB3aGl0ZTsKICBwYWRkaW5nOiA4cHg7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtc2l6ZTogMTFweDsKICBtYXJnaW4tdG9wOiAxMHB4Owp9CgouY29vcmRpbmF0ZS1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiAycHg7Cn0KCi5jb29yZGluYXRlLWl0ZW06bGFzdC1jaGlsZCB7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLm1lYXN1cmUtcmVzdWx0IHsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMTkzLCA3LCAwLjEpOwogIGJvcmRlcjogMXB4IHNvbGlkICNmZmMxMDc7CiAgcGFkZGluZzogOHB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDExcHg7CiAgbWFyZ2luLXRvcDogMTBweDsKfQoKLm1lYXN1cmUtaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogNHB4Owp9CgoubWVhc3VyZS1pdGVtOmxhc3QtY2hpbGQgewogIG1hcmdpbi1ib3R0b206IDhweDsKfQo="}, {"version": 3, "sources": ["MapControls.vue"], "names": [], "mappings": ";AA6ZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "MapControls.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\n  <div class=\"map-controls\">\n    <!-- 图层切换控件 -->\n    <div class=\"control-group layer-control\">\n      <div class=\"control-title\">图层</div>\n      <div class=\"control-buttons\">\n        <el-button\n          v-for=\"layer in availableLayers\"\n          :key=\"layer.key\"\n          :type=\"currentLayer === layer.key ? 'primary' : 'default'\"\n          size=\"mini\"\n          @click=\"switchLayer(layer.key)\"\n        >\n          {{ layer.name }}\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 缩放控件 -->\n    <div class=\"control-group zoom-control\">\n      <div class=\"control-title\">缩放</div>\n      <div class=\"control-buttons\">\n        <el-button size=\"mini\" icon=\"el-icon-plus\" @click=\"zoomIn\" />\n        <el-button size=\"mini\" icon=\"el-icon-minus\" @click=\"zoomOut\" />\n        <el-button size=\"mini\" @click=\"resetView\">重置</el-button>\n      </div>\n    </div>\n\n    <!-- 工具控件 -->\n    <div class=\"control-group tool-control\">\n      <div class=\"control-title\">工具</div>\n      <div class=\"control-buttons\">\n        <el-button\n          size=\"mini\"\n          :type=\"fullscreen ? 'primary' : 'default'\"\n          icon=\"el-icon-full-screen\"\n          @click=\"toggleFullscreen\"\n        >\n          {{ fullscreen ? '退出全屏' : '全屏' }}\n        </el-button>\n        \n        <el-button\n          size=\"mini\"\n          :type=\"showNineLine ? 'primary' : 'default'\"\n          @click=\"toggleNineLine\"\n        >\n          九段线\n        </el-button>\n\n        <el-button\n          size=\"mini\"\n          :type=\"measureMode ? 'primary' : 'default'\"\n          @click=\"toggleMeasure\"\n        >\n          测距\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 显示控件 -->\n    <div class=\"control-group display-control\">\n      <div class=\"control-title\">显示</div>\n      <div class=\"control-buttons\">\n        <el-button\n          size=\"mini\"\n          :type=\"showCoordinates ? 'primary' : 'default'\"\n          @click=\"toggleCoordinates\"\n        >\n          坐标\n        </el-button>\n        \n        <el-button\n          size=\"mini\"\n          :type=\"showScale ? 'primary' : 'default'\"\n          @click=\"toggleScale\"\n        >\n          比例尺\n        </el-button>\n\n        <el-button\n          size=\"mini\"\n          :type=\"showOverview ? 'primary' : 'default'\"\n          @click=\"toggleOverview\"\n        >\n          鹰眼\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 坐标显示 -->\n    <div v-if=\"showCoordinates\" class=\"coordinate-display\">\n      <div class=\"coordinate-item\">\n        <span>经度: {{ currentCoordinates.lng }}</span>\n      </div>\n      <div class=\"coordinate-item\">\n        <span>纬度: {{ currentCoordinates.lat }}</span>\n      </div>\n      <div class=\"coordinate-item\">\n        <span>缩放: {{ currentZoom }}</span>\n      </div>\n    </div>\n\n    <!-- 测距结果显示 -->\n    <div v-if=\"measureMode && measureResult\" class=\"measure-result\">\n      <div class=\"measure-item\">\n        <span>距离: {{ measureResult.distance.toFixed(2) }} 海里</span>\n      </div>\n      <div class=\"measure-item\">\n        <span>方位: {{ measureResult.bearing.toFixed(1) }}°</span>\n      </div>\n      <el-button size=\"mini\" @click=\"clearMeasure\">清除</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapConfig } from '@/utils/map/mapConfig'\nimport { coordinateUtils } from '@/utils/map/coordinateUtils'\nimport { createNineLineLayer, removeNineLineLayer } from '@/utils/map/nineLineData'\n\nexport default {\n  name: 'MapControls',\n  props: {\n    // 地图实例\n    map: {\n      type: Object,\n      required: true\n    },\n    // 当前图层\n    currentLayer: {\n      type: String,\n      default: 'satellite'\n    }\n  },\n  data() {\n    return {\n      fullscreen: false,\n      showNineLine: false,\n      showCoordinates: true,\n      showScale: true,\n      showOverview: false,\n      measureMode: false,\n      measureResult: null,\n      measurePoints: [],\n      measureOverlays: [],\n      nineLineOverlays: [],\n      currentCoordinates: {\n        lng: '0.000000°',\n        lat: '0.000000°'\n      },\n      currentZoom: 0,\n      availableLayers: [\n        { key: 'satellite', name: '卫星图' },\n        { key: 'sea', name: '海图' },\n        { key: 'street', name: '街道图' }\n      ]\n    }\n  },\n  watch: {\n    map: {\n      handler(newMap) {\n        if (newMap) {\n          this.bindMapEvents()\n          this.updateMapInfo()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    /**\n     * 绑定地图事件\n     */\n    bindMapEvents() {\n      if (!this.map) return\n\n      // 鼠标移动事件\n      this.map.on('mousemove', (e) => {\n        if (this.showCoordinates) {\n          this.updateCoordinates(e.lngLat.lng, e.lngLat.lat)\n        }\n      })\n\n      // 缩放事件\n      this.map.on('zoomend', () => {\n        this.updateMapInfo()\n      })\n\n      // 移动事件\n      this.map.on('moveend', () => {\n        this.updateMapInfo()\n      })\n\n      // 点击事件（测距模式）\n      this.map.on('click', (e) => {\n        if (this.measureMode) {\n          this.addMeasurePoint(e.lngLat.lng, e.lngLat.lat)\n        }\n      })\n    },\n\n    /**\n     * 更新地图信息\n     */\n    updateMapInfo() {\n      if (!this.map) return\n\n      this.currentZoom = this.map.getZoom()\n      \n      const center = this.map.getCenter()\n      this.updateCoordinates(center.lng, center.lat)\n    },\n\n    /**\n     * 更新坐标显示\n     */\n    updateCoordinates(lng, lat) {\n      const formatted = coordinateUtils.formatCoordinate(lng, lat)\n      this.currentCoordinates = {\n        lng: formatted.lng,\n        lat: formatted.lat\n      }\n    },\n\n    /**\n     * 切换图层\n     */\n    switchLayer(layerKey) {\n      this.$emit('layer-change', layerKey)\n    },\n\n    /**\n     * 放大\n     */\n    zoomIn() {\n      if (this.map) {\n        const currentZoom = this.map.getZoom()\n        this.map.setZoom(currentZoom + 1)\n      }\n    },\n\n    /**\n     * 缩小\n     */\n    zoomOut() {\n      if (this.map) {\n        const currentZoom = this.map.getZoom()\n        this.map.setZoom(currentZoom - 1)\n      }\n    },\n\n    /**\n     * 重置视图\n     */\n    resetView() {\n      if (this.map) {\n        const center = new BM.LngLat(mapConfig.defaultCenter[0], mapConfig.defaultCenter[1])\n        this.map.setView(center, mapConfig.defaultZoom)\n      }\n    },\n\n    /**\n     * 切换全屏\n     */\n    toggleFullscreen() {\n      this.fullscreen = !this.fullscreen\n      this.$emit('fullscreen-change', this.fullscreen)\n    },\n\n    /**\n     * 切换九段线显示\n     */\n    toggleNineLine() {\n      this.showNineLine = !this.showNineLine\n      \n      if (this.showNineLine) {\n        this.nineLineOverlays = createNineLineLayer(this.map, mapConfig.nineLine)\n      } else {\n        removeNineLineLayer(this.map, this.nineLineOverlays)\n        this.nineLineOverlays = []\n      }\n    },\n\n    /**\n     * 切换坐标显示\n     */\n    toggleCoordinates() {\n      this.showCoordinates = !this.showCoordinates\n    },\n\n    /**\n     * 切换比例尺显示\n     */\n    toggleScale() {\n      this.showScale = !this.showScale\n      this.$emit('scale-change', this.showScale)\n    },\n\n    /**\n     * 切换鹰眼显示\n     */\n    toggleOverview() {\n      this.showOverview = !this.showOverview\n      this.$emit('overview-change', this.showOverview)\n    },\n\n    /**\n     * 切换测距模式\n     */\n    toggleMeasure() {\n      this.measureMode = !this.measureMode\n      \n      if (!this.measureMode) {\n        this.clearMeasure()\n      } else {\n        this.$message.info('点击地图上的两个点进行测距')\n      }\n    },\n\n    /**\n     * 添加测距点\n     */\n    addMeasurePoint(lng, lat) {\n      if (!this.measureMode) return\n\n      this.measurePoints.push({ lng, lat })\n\n      // 添加标记\n      const marker = new BM.Marker(new BM.LngLat(lng, lat), {\n        icon: new BM.Icon({\n          url: '/img/markers/measure-point.png',\n          size: new BM.Size(16, 16),\n          anchor: new BM.Size(8, 8)\n        })\n      })\n      \n      this.map.addOverlay(marker)\n      this.measureOverlays.push(marker)\n\n      // 如果有两个点，计算距离和方位\n      if (this.measurePoints.length === 2) {\n        this.calculateMeasureResult()\n        this.drawMeasureLine()\n      } else if (this.measurePoints.length > 2) {\n        // 重新开始测距\n        this.clearMeasure()\n        this.addMeasurePoint(lng, lat)\n      }\n    },\n\n    /**\n     * 计算测距结果\n     */\n    calculateMeasureResult() {\n      if (this.measurePoints.length < 2) return\n\n      const p1 = this.measurePoints[0]\n      const p2 = this.measurePoints[1]\n\n      const distance = coordinateUtils.calculateDistance(p1.lat, p1.lng, p2.lat, p2.lng)\n      const bearing = coordinateUtils.calculateBearing(p1.lat, p1.lng, p2.lat, p2.lng)\n\n      this.measureResult = {\n        distance,\n        bearing,\n        points: [p1, p2]\n      }\n    },\n\n    /**\n     * 绘制测距线\n     */\n    drawMeasureLine() {\n      if (this.measurePoints.length < 2) return\n\n      const points = this.measurePoints.map(p => new BM.LngLat(p.lng, p.lat))\n      \n      const polyline = new BM.Polyline(points, {\n        strokeColor: mapConfig.measurement.distance.strokeColor,\n        strokeWeight: mapConfig.measurement.distance.strokeWeight,\n        strokeOpacity: mapConfig.measurement.distance.strokeOpacity,\n        strokeStyle: 'dashed'\n      })\n\n      this.map.addOverlay(polyline)\n      this.measureOverlays.push(polyline)\n    },\n\n    /**\n     * 清除测距\n     */\n    clearMeasure() {\n      // 清除覆盖物\n      this.measureOverlays.forEach(overlay => {\n        this.map.removeOverlay(overlay)\n      })\n      \n      // 重置数据\n      this.measurePoints = []\n      this.measureOverlays = []\n      this.measureResult = null\n    }\n  },\n\n  beforeDestroy() {\n    // 清理资源\n    this.clearMeasure()\n    removeNineLineLayer(this.map, this.nineLineOverlays)\n  }\n}\n</script>\n\n<style scoped>\n.map-controls {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\n  padding: 15px;\n  min-width: 200px;\n  max-width: 300px;\n}\n\n.control-group {\n  margin-bottom: 15px;\n}\n\n.control-group:last-child {\n  margin-bottom: 0;\n}\n\n.control-title {\n  font-size: 12px;\n  font-weight: bold;\n  color: #666;\n  margin-bottom: 8px;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 4px;\n}\n\n.control-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n}\n\n.coordinate-display {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  margin-top: 10px;\n}\n\n.coordinate-item {\n  margin-bottom: 2px;\n}\n\n.coordinate-item:last-child {\n  margin-bottom: 0;\n}\n\n.measure-result {\n  background: rgba(255, 193, 7, 0.1);\n  border: 1px solid #ffc107;\n  padding: 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  margin-top: 10px;\n}\n\n.measure-item {\n  margin-bottom: 4px;\n}\n\n.measure-item:last-child {\n  margin-bottom: 8px;\n}\n</style>\n"]}]}